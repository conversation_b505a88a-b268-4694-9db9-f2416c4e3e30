import 'package:flutter/material.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:provider/provider.dart';

class CreateObjectDetailsMobile extends StatefulWidget {
  const CreateObjectDetailsMobile({super.key});

  @override
  State<CreateObjectDetailsMobile> createState() =>
      _CreateObjectDetailsMobileState();
}

// Data model for attributes
class AttributeModel {
  final String name;
  final String displayName;
  final String dataType;
  final bool isRequired;
  final bool isUnique;

  AttributeModel({
    required this.name,
    required this.displayName,
    required this.dataType,
    required this.isRequired,
    required this.isUnique,
  });
}

class _CreateObjectDetailsMobileState extends State<CreateObjectDetailsMobile> {
  final TextEditingController _objectController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String? _selectedType;
  String? _selectedIcon;
  bool _showExpansionPanels =
      false; // Start with form, show panels after validation
  Set<String> _expandedPanels = {}; // Track which panels are expanded

  // List to store attributes
  List<AttributeModel> _attributes = [];

  final List<String> _typeOptions = [
    'Entity',
    'Service',
    'Component',
    'Interface',
    'Process',
  ];

  @override
  void dispose() {
    _objectController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content area
                Expanded(
                  child: _buildContent(context, provider),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 24,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 16),
          // Title
          Expanded(
            child: Text(
              'Create Object Details',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleSmall(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.white,
                fontWeight: FontWeight.bold,
                height: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, ObjectCreationProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - 200,
            ),
            child: _buildContentWithLineNumbers(context, provider),
          ),
        ),
      ),
    );
  }

  Widget _buildContentWithLineNumbers(
      BuildContext context, ObjectCreationProvider provider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];

    if (!_showExpansionPanels) {
      // Line 1: Object Detail header
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildObjectDetailHeader()));
      allWidgets.add(const SizedBox(height: 16));

      // Line 2: Object input field
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildObjectField()));
      allWidgets.add(const SizedBox(height: 16));

      // Line 3: Select Type and Icon row
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildTypeAndIconRow()));
      allWidgets.add(const SizedBox(height: 16));

      // Line 4: Description field
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, _buildDescriptionField()));
      allWidgets.add(const SizedBox(height: 24));

      // Validate button (without line number)
      allWidgets.add(_buildValidateButton(provider));
    } else {
      // Show expansion panels after validation - continue line numbering from 1
      // Line 1: Object: Customer
      allWidgets.add(_buildLineWithNumber(lineNumber++, _buildObjectHeader()));
      allWidgets.add(const SizedBox(height: 16));
      allWidgets.addAll(_buildExpansionPanelsWithLineNumbers(lineNumber));
    }

    return Stack(
      children: [
        // Continuous vertical line
        // Positioned(
        //   left: 28, // Position after line number (20px width + 8px margin)
        //   top: 0,
        //   bottom: 0,
        //   child: Container(
        //     width: 1,
        //     color: Colors.grey.shade300,
        //   ),
        // ),
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
      ],
    );
  }

  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Line number
        Container(
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildObjectDetailHeader() {
    return Text(
      'Object Detail',
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w700,
        color: Colors.black,
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
    );
  }

  Widget _buildObjectField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Object',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 48,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: TextField(
            controller: _objectController,
            decoration: const InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              fillColor: Colors.transparent,
              hoverColor: Colors.transparent,
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 14),
              isDense: true,
            ),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeAndIconRow() {
    return Row(
      children: [
        // Select Type section
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Type',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 48,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.white,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedType,
                    hint: Text(
                      'Select Type',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Colors.grey.shade600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                    icon: Icon(Icons.keyboard_arrow_down,
                        color: Colors.grey.shade600),
                    isExpanded: true,
                    items: _typeOptions.map((String type) {
                      return DropdownMenuItem<String>(
                        value: type,
                        child: Text(
                          type,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedType = newValue;
                      });
                    },
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // Icon section
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Icon (64x64 Pixel):',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 48,
                child: ElevatedButton(
                  onPressed: () {
                    // Handle browse functionality
                    _showIconSelector(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade100,
                    foregroundColor: Colors.black,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                      side: BorderSide(color: Colors.grey.shade400),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Browse',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: TextField(
            controller: _descriptionController,
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              fillColor: Colors.transparent,
              hoverColor: Colors.transparent,
              contentPadding: const EdgeInsets.all(12),
              hintText:
                  'Comprehensive customer onboarding process from registration to welcome completion',
              hintStyle: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade600,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildValidateButton(ObjectCreationProvider provider) {
    return Center(
      child: Container(
        width: double.infinity,
        height: 48,
        margin: const EdgeInsets.symmetric(horizontal: 32),
        child: ElevatedButton(
          onPressed: () {
            _handleValidation(provider);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF007AFF),
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          child: Text(
            'Validate',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ),
    );
  }

  void _showIconSelector(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Icon',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: GridView.count(
                  crossAxisCount: 4,
                  children: [
                    _buildIconOption(Icons.person, 'Person'),
                    _buildIconOption(Icons.business, 'Business'),
                    _buildIconOption(Icons.settings, 'Settings'),
                    _buildIconOption(Icons.home, 'Home'),
                    _buildIconOption(Icons.email, 'Email'),
                    _buildIconOption(Icons.phone, 'Phone'),
                    _buildIconOption(Icons.location_on, 'Location'),
                    _buildIconOption(Icons.calendar_today, 'Calendar'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildIconOption(IconData icon, String name) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedIcon = name;
        });
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 24, color: Colors.grey.shade700),
            const SizedBox(height: 4),
            Text(
              name,
              style: TextStyle(
                fontSize: 10,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectHeader() {
    return Row(
      children: [
        Text(
          'Object: ',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        Text(
          'Customer',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF007AFF),
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildExpansionPanelsWithLineNumbers(int startingLineNumber) {
    List<Widget> widgets = [];
    int lineNumber = startingLineNumber;

    // Line 2: Attributes Details Section
    widgets.add(_buildLineWithNumber(
        lineNumber++,
        _buildExpansionPanel(
          title: 'Attributes Details',
          subtitle: '${_attributes.length} Attributes',
          children: [_buildAttributesTable()],
        )));
    widgets.add(const SizedBox(height: 16));

    // Line 3: Entity Relationship Section
    widgets.add(_buildLineWithNumber(
        lineNumber++,
        _buildExpansionPanel(
          title: 'Entity Relationship',
          subtitle: '0 Rules Configured',
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'No relationships configured',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey.shade600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ),
            ),
          ],
        )));
    widgets.add(const SizedBox(height: 16));

    // Line 4: Enumerated Values Section
    widgets.add(_buildLineWithNumber(
        lineNumber++,
        _buildExpansionPanel(
          title: 'Enumerated Values',
          subtitle: '0 Rules Configured',
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'No enumerated values configured',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey.shade600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ),
            ),
          ],
        )));

    return widgets;
  }

  Widget _buildExpansionPanel({
    required String title,
    required String subtitle,
    required List<Widget> children,
  }) {
    final bool isExpanded = _expandedPanels.contains(title);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF007AFF) : Colors.grey.shade300,
          width: isExpanded ? .5 : .5,
        ),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          tilePadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          childrenPadding: EdgeInsets.zero,
          trailing: Transform.translate(
            offset: const Offset(0, -4),
            child: GestureDetector(
              onTap: () {
                if (title == 'Attributes Details') {
                  _showAddAttributeModal();
                }
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade400, width: 1),
                ),
                child: Icon(
                  Icons.add,
                  size: 16,
                  color: Colors.grey.shade700,
                ),
              ),
            ),
          ),
          title: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade600,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
          children: children,
          onExpansionChanged: (bool expanded) {
            setState(() {
              if (expanded) {
                _expandedPanels.add(title);
              } else {
                _expandedPanels.remove(title);
              }
            });
          },
        ),
      ),
    );
  }

  Widget _buildAttributesTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(3),
                topRight: Radius.circular(3),
              ),
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'Name',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Display Name',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Data Type',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
                Container(
                  width: 50,
                  alignment: Alignment.center,
                  child: Text(
                    'Action',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Dynamic Attribute Rows
          if (_attributes.isEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'No attributes configured. Click the + icon to add attributes.',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey.shade600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            )
          else
            ..._attributes.asMap().entries.map((entry) {
              int index = entry.key;
              AttributeModel attribute = entry.value;
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: index < _attributes.length - 1
                        ? BorderSide(color: Colors.grey.shade200, width: 1)
                        : BorderSide.none,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        attribute.name,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        attribute.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Text(
                        attribute.dataType,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                    Container(
                      width: 50,
                      alignment: Alignment.center,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border:
                              Border.all(color: Colors.red.shade200, width: 1),
                        ),
                        child: IconButton(
                          onPressed: () {
                            _removeAttribute(index);
                          },
                          icon: Icon(
                            Icons.delete_outline,
                            size: 12,
                            color: Colors.red.shade600,
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
        ],
      ),
    );
  }

  void _handleValidation(ObjectCreationProvider provider) {
    if (_objectController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter object name');
      return;
    }

    if (_selectedType == null) {
      _showErrorSnackBar('Please select a type');
      return;
    }

    if (_descriptionController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter description');
      return;
    }

    // Show expansion panels after successful validation
    setState(() {
      _showExpansionPanels = true;
    });

    _showSuccessSnackBar('Object details validated successfully!');
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showAddAttributeModal() {
    final TextEditingController nameController =
        TextEditingController(text: 'Customer');
    final TextEditingController displayNameController =
        TextEditingController(text: 'Customer ID');
    final TextEditingController defaultController =
        TextEditingController(text: 'primary');
    String selectedDataType = 'String';
    String selectedRequired = 'No';
    String selectedUnique = 'key';

    final List<String> dataTypes = [
      'String',
      'Integer',
      'Boolean',
      'Date',
      'Double'
    ];

    final List<String> requiredOptions = ['Yes', 'No'];
    final List<String> uniqueOptions = ['key', 'primary', 'default'];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.75,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      border: Border(
                        bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                      ),
                    ),
                    child: Row(
                      children: [
                        Text(
                          'Validation Rules',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                            fontFamily: FontManager.fontFamilyTiemposText,
                          ),
                        ),
                        const Spacer(),
                        GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                  color: Colors.grey.shade300, width: 1),
                            ),
                            child: Icon(
                              Icons.close,
                              color: Colors.grey.shade600,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Form Content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Name field
                          _buildBottomSheetField(
                            label: 'Name',
                            controller: nameController,
                            hintText: 'Customer',
                          ),
                          const SizedBox(height: 20),

                          // Display Name field
                          _buildBottomSheetField(
                            label: 'Display Name',
                            controller: displayNameController,
                            hintText: 'Customer ID',
                          ),
                          const SizedBox(height: 20),

                          // Data Type and Required row
                          Row(
                            children: [
                              // Data Type dropdown
                              Expanded(
                                child: _buildBottomSheetDropdown(
                                  label: 'Data Type',
                                  value: selectedDataType,
                                  items: dataTypes,
                                  onChanged: (String? newValue) {
                                    setModalState(() {
                                      selectedDataType = newValue!;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Required dropdown
                              Expanded(
                                child: _buildBottomSheetDropdown(
                                  label: 'Required',
                                  value: selectedRequired,
                                  items: requiredOptions,
                                  onChanged: (String? newValue) {
                                    setModalState(() {
                                      selectedRequired = newValue!;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          // Unique and Default row
                          Row(
                            children: [
                              // Unique dropdown
                              Expanded(
                                child: _buildBottomSheetDropdown(
                                  label: 'Unique',
                                  value: selectedUnique,
                                  items: uniqueOptions,
                                  onChanged: (String? newValue) {
                                    setModalState(() {
                                      selectedUnique = newValue!;
                                    });
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Default field
                              Expanded(
                                child: _buildBottomSheetField(
                                  label: 'Default',
                                  controller: defaultController,
                                  hintText: 'primary',
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Buttons
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 48,
                            child: OutlinedButton(
                              onPressed: () => Navigator.pop(context),
                              style: OutlinedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: Colors.black,
                                side: BorderSide(
                                    color: Colors.grey.shade300, width: 1),
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Cancel',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            height: 48,
                            child: ElevatedButton(
                              onPressed: () {
                                if (nameController.text.trim().isNotEmpty &&
                                    displayNameController.text
                                        .trim()
                                        .isNotEmpty) {
                                  _addAttribute(
                                    nameController.text.trim(),
                                    displayNameController.text.trim(),
                                    selectedDataType,
                                    selectedRequired == 'Yes',
                                    selectedUnique == 'primary',
                                  );
                                  Navigator.pop(context);
                                } else {
                                  _showErrorSnackBar(
                                      'Please fill all required fields');
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF007AFF),
                                foregroundColor: Colors.white,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Apply This',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildModalField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    double? height,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: height ?? 48,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              fillColor: Colors.transparent,
              hoverColor: Colors.transparent,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
              hintText: hintText,
              hintStyle: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade600,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              isDense: true,
            ),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomSheetField({
    required String label,
    required TextEditingController controller,
    required String hintText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 48,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: TextField(
            controller: controller,
            decoration: InputDecoration(
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              fillColor: Colors.transparent,
              hoverColor: Colors.transparent,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
              hintText: hintText,
              hintStyle: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade500,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
              isDense: true,
            ),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomSheetDropdown({
    required String label,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 48,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              icon: Icon(Icons.keyboard_arrow_down,
                  color: Colors.grey.shade600, size: 20),
              isExpanded: true,
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(
                    item,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ],
    );
  }

  void _addAttribute(String name, String displayName, String dataType,
      bool isRequired, bool isUnique) {
    setState(() {
      _attributes.add(AttributeModel(
        name: name,
        displayName: displayName,
        dataType: dataType,
        isRequired: isRequired,
        isUnique: isUnique,
      ));
    });
    _showSuccessSnackBar('Attribute added successfully!');
  }

  void _removeAttribute(int index) {
    setState(() {
      _attributes.removeAt(index);
    });
    _showSuccessSnackBar('Attribute removed successfully!');
  }
}
